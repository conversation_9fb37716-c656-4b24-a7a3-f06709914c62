# import cv2
# import numpy as np
# import json
# import os
# from glob import glob

# def load_camera_params_from_json(json_path):
#     with open(json_path, "r") as f:
#         params = json.load(f)

#     intrinsics = params["camera_parameters"]
#     fx = intrinsics["camera_matrix"]["fx"]
#     fy = intrinsics["camera_matrix"]["fy"]
#     cx = intrinsics["camera_matrix"]["cx"]
#     cy = intrinsics["camera_matrix"]["cy"]

#     camera_matrix = np.array([[fx, 0, cx],
#                               [0, fy, cy],
#                               [0, 0, 1]], dtype=np.float64)

#     dist_coeffs = np.array([
#         intrinsics["distortion_coefficients"]["radial"]["k1"],
#         intrinsics["distortion_coefficients"]["radial"]["k2"],
#         intrinsics["distortion_coefficients"]["tangential"]["p1"],
#         intrinsics["distortion_coefficients"]["tangential"]["p2"],
#         intrinsics["distortion_coefficients"]["radial"]["k3"]
#     ], dtype=np.float64)

#     board_size = tuple(params["chessboard_info"]["board_size"])
#     return camera_matrix, dist_coeffs, board_size

# def prepare_object_points(board_size, square_size, board_origin_in_robot):
#     objp = np.zeros((board_size[0]*board_size[1], 3), np.float32)
#     objp[:, :2] = np.mgrid[0:board_size[0], 0:board_size[1]].T.reshape(-1, 2)
#     objp = objp * square_size
#     objp_world = objp + board_origin_in_robot  # 广播加法，所有角点绝对坐标
#     return objp_world

# def find_chessboard_corners(gray_image, board_size):
#     ret, corners = cv2.findChessboardCorners(gray_image, board_size)
#     if not ret:
#         return None
#     criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
#     return cv2.cornerSubPix(gray_image, corners, (11,11), (-1,-1), criteria)

# def compute_extrinsics(objp, img_points, camera_matrix, dist_coeffs):
#     success, rvec, tvec = cv2.solvePnP(objp, img_points, camera_matrix, dist_coeffs,flags=cv2.SOLVEPNP_ITERATIVE)
#     return (rvec, tvec) if success else (None, None)

# def compute_reprojection_error(objp, img_points, rvec, tvec, camera_matrix, dist_coeffs):
#     projected, _ = cv2.projectPoints(objp, rvec, tvec, camera_matrix, dist_coeffs)
#     error = np.linalg.norm(img_points.reshape(-1,2) - projected.reshape(-1,2), axis=1)
#     return np.mean(error)
    

# def compute_extrinsic_world_pose(rvec, tvec):
#     """由相机在棋盘系下的位置 → 推出相机在世界坐标下的位姿"""
#     R, _ = cv2.Rodrigues(rvec)
#     R_world = R.T
#     t_world = -R_world @ tvec
#     return R_world, t_world


# def save_extrinsics_to_json(rvec, tvec, R, error, save_path):
#     data = {
#         "extrinsics": {
#             "rotation_vector": rvec.ravel().tolist(),     # 转为 list
#             "translation_vector": tvec.ravel().tolist(),  # 转为 list
#             "rotation_matrix": R.tolist(),                # 转为 list of list
#             "reprojection_error": float(error)            # 转为 Python float
#         }
#     }
#     with open(save_path, "w") as f:
#         json.dump(data, f, indent=4)

# def save_extrinsic_to_json(R, t, save_path):
#     data = {
#         "rotation_matrix_world": R.tolist(),
#         "translation_vector_world": t.reshape(-1).tolist()
#     }
#     with open(save_path, "w") as f:
#         json.dump(data, f, indent=4)
#     print(f"外参保存至：{save_path}")

# import cv2
# import numpy as np

# def calibrate_camera_to_robot(objpoints, imgpoints, camera_matrix, dist_coeffs):
#     """
#     输入:
#         objpoints: 棋盘格世界坐标点列表 (m x 3)，单位 m 或 mm
#         imgpoints: 棋盘格图像像素点列表
#         camera_matrix: 内参矩阵
#         dist_coeffs: 畸变系数
#     输出:
#         rvec_robot: 相机相对于机器人坐标系的旋转向量
#         tvec_robot: 相机相对于机器人坐标系的平移向量 (XYZ单位同objpoints)
#     """

#     # === 1. 用 OpenCV 默认世界坐标系求解相机位姿 ===
#     ret, rvec, tvec = cv2.solvePnP(objpoints, imgpoints, camera_matrix, dist_coeffs)

#     # === 2. 将旋转向量转成旋转矩阵 ===
#     R_cb, _ = cv2.Rodrigues(rvec)  # 棋盘格 -> 相机

#     # === 3. 棋盘格系 -> 机器人系的旋转矩阵 ===
#     # 默认棋盘系: X右, Y下, Z朝相机
#     # 机器人系: X右, Y前, Z上
#     # 转换: Y棋盘 -> -Z机器人, Z棋盘 -> Y机器人
#     R_board_to_robot = np.array([
#         [1,  0,  0],   # X保持不变
#         [0,  0,  1],   # 原Z变成Y
#         [0, -1,  0]    # 原Y向下变成-Z
#     ], dtype=np.float64)

#     # === 4. 棋盘 -> 相机  转  机器人 -> 相机
#     R_rb = R_cb @ np.linalg.inv(R_board_to_robot)

#     # === 5. 相机 -> 机器人 取逆
#     R_br = R_rb.T
#     tvec_robot = -R_br @ tvec  # 平移向量

#     # === 6. 转回旋转向量
#     rvec_robot, _ = cv2.Rodrigues(R_br)

#     return rvec_robot, tvec_robot, ret  



# def process_images(image_paths, json_path, square_size, save_path):
#     camera_matrix, dist_coeffs, board_size = load_camera_params_from_json(json_path)
#     board_origin_in_robot = np.array([0, 0, 0])  # 棋盘格原点在机器人坐标系中的位置
#     objp = prepare_object_points(board_size, square_size, board_origin_in_robot)

#     best_error = float('inf')
#     best_rvec, best_tvec, best_R = None, None, None
#     best_image = ""

#     for img_path in image_paths:
#         img = cv2.imread(img_path)
#         if img is None:
#             print(f"跳过无法读取的图片: {img_path}")
#             continue

#         gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
#         corners = find_chessboard_corners(gray, board_size)
#         if corners is None:
#             print(f"未检测到角点: {img_path}")
#             continue
#         cv2.drawChessboardCorners(img, board_size, corners, True)
#         base_name = os.path.splitext(os.path.basename(img_path))[0]
#         save_path1 = f'{base_name}_corners.jpg'
#         cv2.imwrite(save_path1, img)
#         rvec, tvec, ret = calibrate_camera_to_robot(objp, corners, camera_matrix, dist_coeffs)

#         # rvec, tvec = compute_extrinsics(objp, corners, camera_matrix, dist_coeffs)
#         if rvec is None:
#             print(f"计算外参失败: {img_path}")
#             continue

#         # error = compute_reprojection_error(objp, corners, rvec, tvec, camera_matrix, dist_coeffs)
#         print(f"{os.path.basename(img_path)} 重投影误差: {ret:.4f}")

#         if ret < best_error:
#             best_error = ret
#             best_rvec, best_tvec = rvec, tvec
#             best_R, _ = cv2.Rodrigues(rvec)
#             best_image = img_path

#     if best_rvec is not None:
#         print(f"\n选择误差最小图像: {best_image}")
#         save_extrinsics_to_json(best_rvec, best_tvec, best_R, best_error, "./best_extrinsics.json")
#     else:
#         print("未能从任何图像中成功提取外参。")

#     # R_world, t_world = compute_extrinsic_world_pose(best_rvec, best_tvec)
#     print("\n相机在世界坐标系下的外参：")
#     print("旋转矩阵 R：\n", best_R)
#     print("平移向量 t：\n", best_tvec)
#     # 保存
#     save_extrinsic_to_json(best_R, best_tvec, save_path)

# if __name__ == "__main__":
#     # === 参数区 ===
#     intrinsics_json = "/home/<USER>/panpan/code/Calib/camera_intrinsic/0807/calib_intrix_new.json"
#     # image_dir = "/home/<USER>/panpan/code/Calib/camera_intrinsic/extrinsic/img"
#     img_path = "../20250808-130507.bmp"
#     save_extrinsic_path = "world_extrinsics.json"
#     square_size_mm = 30.0

#     # === 获取所有图像路径 ===
#     # image_paths = sorted(glob(os.path.join(image_dir, "*.bmp")))
#     image_paths = [img_path]

#     process_images(image_paths, intrinsics_json, square_size_mm, save_extrinsic_path)


import cv2
import numpy as np
import json
import os
from glob import glob

def load_camera_params_from_json(json_path):
    with open(json_path, "r") as f:
        params = json.load(f)

    intrinsics = params["camera_parameters"]
    fx = intrinsics["camera_matrix"]["fx"]
    fy = intrinsics["camera_matrix"]["fy"]
    cx = intrinsics["camera_matrix"]["cx"]
    cy = intrinsics["camera_matrix"]["cy"]

    camera_matrix = np.array([[fx, 0, cx],
                              [0, fy, cy],
                              [0, 0, 1]], dtype=np.float64)

    dist_coeffs = np.array([
        intrinsics["distortion_coefficients"]["radial"]["k1"],
        intrinsics["distortion_coefficients"]["radial"]["k2"],
        intrinsics["distortion_coefficients"]["tangential"]["p1"],
        intrinsics["distortion_coefficients"]["tangential"]["p2"],
        intrinsics["distortion_coefficients"]["radial"]["k3"]
    ], dtype=np.float64)

    board_size = tuple(params["chessboard_info"]["board_size"])
    return camera_matrix, dist_coeffs, board_size

def prepare_object_points(board_size, square_size, board_origin_in_robot):
    objp = np.zeros((board_size[0]*board_size[1], 3), np.float32)
    objp[:, :2] = np.mgrid[0:board_size[0], 0:board_size[1]].T.reshape(-1, 2)
    objp = objp * square_size
    objp_world = objp + board_origin_in_robot
    return objp_world

def find_chessboard_corners(gray_image, board_size):
    ret, corners = cv2.findChessboardCorners(gray_image, board_size)
    if not ret:
        return None
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
    return cv2.cornerSubPix(gray_image, corners, (11,11), (-1,-1), criteria)

def compute_extrinsics(objp, img_points, camera_matrix, dist_coeffs):
    success, rvec, tvec = cv2.solvePnP(objp, img_points, camera_matrix, dist_coeffs,
                                       flags=cv2.SOLVEPNP_ITERATIVE)
    return (rvec, tvec) if success else (None, None)

def compute_reprojection_error(objp, img_points, rvec, tvec, camera_matrix, dist_coeffs):
    projected, _ = cv2.projectPoints(objp, rvec, tvec, camera_matrix, dist_coeffs)
    error = np.linalg.norm(img_points.reshape(-1,2) - projected.reshape(-1,2), axis=1)
    return np.mean(error)

def compute_extrinsic_robot_pose(rvec, tvec):
    """
    将 solvePnP 得到的棋盘系 -> 相机系 转换为 相机系 -> 机器人系
    机器人坐标系定义: X 右, Y 前, Z 上
    """
    # 旋转向量 -> 旋转矩阵（棋盘 -> 相机）
    R_cb, _ = cv2.Rodrigues(rvec)

    # 棋盘系 -> 机器人系的旋转矩阵
    # OpenCV 默认棋盘系: X右, Y下, Z朝相机
    # 机器人系: X右, Y前, Z上
    R_board_to_robot = np.array([
        [1,  0,  0],  # X保持不变
        [0,  0,  1],  # Z棋盘 -> Y机器人
        [0, -1,  0]   # Y棋盘 -> -Z机器人
    ], dtype=np.float64)

    # 棋盘 -> 相机 转成 机器人 -> 相机
    R_rc = R_cb @ np.linalg.inv(R_board_to_robot)

    # 相机 -> 机器人取逆
    R_cr = R_rc.T
    t_cr = -R_cr @ tvec

    return R_cr, t_cr

def save_extrinsics_to_json(rvec, tvec, R, error, save_path):
    data = {
        "extrinsics": {
            "rotation_vector": rvec.ravel().tolist(),
            "translation_vector": tvec.ravel().tolist(),
            "rotation_matrix": R.tolist(),
            "reprojection_error": float(error)
        }
    }
    with open(save_path, "w") as f:
        json.dump(data, f, indent=4)

def save_extrinsic_to_json(R, t, save_path):
    data = {
        "rotation_matrix_robot": R.tolist(),
        "translation_vector_robot": t.reshape(-1).tolist()
    }
    with open(save_path, "w") as f:
        json.dump(data, f, indent=4)
    print(f"外参保存至：{save_path}")

def process_images(image_paths, json_path, square_size, save_path):
    camera_matrix, dist_coeffs, board_size = load_camera_params_from_json(json_path)
    board_origin_in_robot = np.array([0, 0, 0])
    objp = prepare_object_points(board_size, square_size, board_origin_in_robot)

    best_error = float('inf')
    best_rvec, best_tvec, best_R = None, None, None
    best_image = ""

    for img_path in image_paths:
        img = cv2.imread(img_path)
        if img is None:
            print(f"跳过无法读取的图片: {img_path}")
            continue

        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        corners = find_chessboard_corners(gray, board_size)
        if corners is None:
            print(f"未检测到角点: {img_path}")
            continue

        cv2.drawChessboardCorners(img, board_size, corners, True)
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        cv2.imwrite(f'{base_name}_corners.jpg', img)

        rvec, tvec = compute_extrinsics(objp, corners, camera_matrix, dist_coeffs)
        if rvec is None:
            print(f"计算外参失败: {img_path}")
            continue

        error = compute_reprojection_error(objp, corners, rvec, tvec, camera_matrix, dist_coeffs)
        print(f"{os.path.basename(img_path)} 重投影误差: {error:.4f}")

        if error < best_error:
            best_error = error
            best_rvec, best_tvec = rvec, tvec
            best_R, _ = cv2.Rodrigues(rvec)
            best_image = img_path

    if best_rvec is not None:
        print(f"\n选择误差最小图像: {best_image}")
        save_extrinsics_to_json(best_rvec, best_tvec, best_R, best_error, "./best_extrinsics.json")
    else:
        print("未能从任何图像中成功提取外参。")
        return

    # 转换到机器人系
    R_robot, t_robot = compute_extrinsic_robot_pose(best_rvec, best_tvec)
    print("\n相机在机器人坐标系下的外参：")
    print("旋转矩阵 R：\n", R_robot)
    print("平移向量 t (XYZ, 单位同棋盘格)：\n", t_robot.T)

    save_extrinsic_to_json(R_robot, t_robot, save_path)

if __name__ == "__main__":
    intrinsics_json = "/home/<USER>/panpan/code/Calib/camera_intrinsic/0807/calib_intrix_new.json"
    img_path = "../20250808-130507.bmp"
    save_extrinsic_path = "robot_extrinsics.json"
    square_size_mm = 30.0

    image_paths = [img_path]
    process_images(image_paths, intrinsics_json, square_size_mm, save_extrinsic_path)
